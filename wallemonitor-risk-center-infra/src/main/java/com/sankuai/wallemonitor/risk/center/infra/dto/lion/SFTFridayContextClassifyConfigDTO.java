package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Friday SFT 分类配置DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SFTFridayContextClassifyConfigDTO {

    /**
     * 系统提示词
     */
    private String systemPrompt;

    /**
     * 用户提示词模板
     */
    private String userPromptTemplate;

    /**
     * 模型名称
     */
    @Builder.Default
    private String modelName = "context_risk_check_qwen_2_5_32";

    /**
     * APPID
     */
    @Builder.Default
    private String appId = "1840301155554250788";

    /**
     * 超时时间（秒）
     */
    @Builder.Default
    private Integer timeout = 10;

    /**
     * 接管值
     */
    @Builder.Default
    private String takeOverValue = "是";

    /**
     * 不接管值
     */
    @Builder.Default
    private String notTakeOverValue = "否";

    /**
     * 默认接管分类
     */
    @Builder.Default
    private ISCheckCategoryEnum defaultTakeOverCategory = ISCheckCategoryEnum.GOOD_OTHER;

    /**
     * 默认不接管分类
     */
    @Builder.Default
    private ISCheckCategoryEnum defaultNotTakeOverCategory = ISCheckCategoryEnum.BAD_OTHER;

    /**
     * 增强解析模板
     */
    private String enhancedParseTemplate;

    /**
     * 视角点列表
     */
    private List<String> viewPoints;

    /**
     * 是否启用车辆上下文
     */
    @Builder.Default
    private Boolean enableVehicleContext = true;

    /**
     * 车辆上下文占位符
     */
    @Builder.Default
    private String vehicleContextPlaceholder = "{}";

    /**
     * 车辆信息模板
     */
    private String vehicleInfoTemplate;

    /**
     * 别名到分类的映射（中文名称 → 英文代码）
     */
    private Map<String, String> alias2category;

    /**
     * 解析模板
     */
    private String parseTemplate;

    /**
     * 检查配置是否有效
     *
     * @return 配置是否有效
     */
    public boolean isValid() {
        return userPromptTemplate != null &&
               systemPrompt != null &&
               modelName != null &&
               appId != null;
    }

    /**
     * 构建包含车辆上下文的用户提示词
     *
     * @param vehicleContext 车辆上下文信息
     * @return 包含上下文的用户提示词
     */
    public String buildUserPromptWithContext(String vehicleContext) {
        if (!enableVehicleContext || vehicleContext == null || userPromptTemplate == null) {
            return userPromptTemplate;
        }

        return userPromptTemplate.replace(vehicleContextPlaceholder, vehicleContext);
    }
}
