package com.sankuai.wallemonitor.risk.center.infra.vto.result;

import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Friday AI 上下文分类结果
 * 包含预测的分类信息
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SFTFridayContextClassifyResultVTO {

    @Builder.Default
    private List<SFTFridayContextClassifyResultVTOItem> data = new ArrayList<>();

    private SFTFridayContextClassifyResultVTOItem finalPredict;

    /**
     * 分类结果信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ClassifyInfo {
        /**
         * 分类类型
         */
        private String category;

        /**
         * 是否需要接管
         */
        private Boolean shouldBeTakeOver;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SFTFridayContextClassifyResultVTOItem {
        @Builder.Default
        private List<String> imageUrls = new ArrayList<>();

        private ISCheckCategoryEnum checkCategory;

        private Date checkStartTime;

        private Date checkEndTime;

        /**
         * 预测信息
         */
        private ClassifyInfo predictedInfo;
    }
}
