package com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.meituan.xframe.config.annotation.ConfigValueListener;
import com.meituan.xframe.config.vo.ConfigChangedEvent;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.util.VehicleContextExtractor;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.FridayOpenAiAdapter;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.SFTFridayContextClassifyConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.ISCheckCategoryEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.check.CheckUtil;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.FridayModelParamVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.SFTFridayContextClassifyResultVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.SFTFridayContextClassifyResultVTO.SFTFridayContextClassifyResultVTOItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Friday AI 上下文分类器
 * 基于车辆运行时上下文信息进行智能分类判断
 */
@Component
@Slf4j
public class ISAIFridayContextClassify implements ISCheckAction<SFTFridayContextClassifyResultVTO> {

    @Resource
    private FridayOpenAiAdapter fridayOpenAiAdapter;

    @Resource
    private SFTFridayContextClassifyConfigDTO sftFridayContextClassifyConfigDTO;

    private Pattern extractModelResultPattern;

    @Override
    public ISCheckActionResult<SFTFridayContextClassifyResultVTO> execute(ISCheckActionContext actionContext) {
        sftFridayContextClassifyConfigDTO=actionContext.getCurrentActionConfig(sftFridayContextClassifyConfigDTO.class)
        
        RiskCheckingQueueItemDO item = actionContext.getItem();
        if (item == null) {
            log.info("Friday Context Classify - 没有预检队列信息，直接返回");
            // 没有预检队列信息，直接返回
            return ISCheckActionResult.empty();
        }

        // 停滞开始时间
        Date startTime = Optional.ofNullable(item.getOccurTime()).orElse(new Date());

        // 车架号
        String vin = Optional.ofNullable(actionContext.getVehicleRunTimeContext())
                .map(VehicleRuntimeInfoContextDO::getVin).orElse(null);

        if (StringUtils.isEmpty(vin)) {
            log.info("Friday Context Classify - 没有车架号，直接返回");
            return ISCheckActionResult.empty();
        }

        // 提取车辆上下文信息
        String vehicleContext = extractVehicleContext(actionContext);
        log.info("Friday Context Classify - 提取的车辆上下文信息: vin={}, context={}", vin, vehicleContext);

        FridayModelParamVTO fridayModelParamVTO = FridayModelParamVTO.builder()
                .modelName(sftFridayContextClassifyConfigDTO.getModelName())
                .appId(sftFridayContextClassifyConfigDTO.getAppId())
                .timeout(sftFridayContextClassifyConfigDTO.getTimeout())
                .occurTime(startTime)
                .vin(vin)
                .build();

        SFTFridayContextClassifyResultVTO contextClassifyResult = verifySFTContextResult(fridayModelParamVTO, vehicleContext);

        ISCheckCategoryEnum checkCategoryEnum = Optional.of(contextClassifyResult)
                .map(SFTFridayContextClassifyResultVTO::getFinalPredict)
                .filter(Objects::nonNull)
                .map(SFTFridayContextClassifyResultVTOItem::getCheckCategory)
                .orElse(null);

        if (checkCategoryEnum == null) {
            // 没有CheckCategory ,返回空检查结果
            log.info("Friday Context Classify - 没有CheckCategory ,返回空检查结果");
            return ISCheckActionResult.empty();
        }

        return ISCheckActionResult.<SFTFridayContextClassifyResultVTO>builder()
                .categoryEnum(checkCategoryEnum).actionResult(contextClassifyResult).build();
    }

    /**
     * 从上下文中提取车辆运行时信息
     * 
     * @param actionContext 检查动作上下文
     * @return 格式化的车辆上下文信息字符串
     */
    private String extractVehicleContext(ISCheckActionContext actionContext) {
        try {
            VehicleRuntimeInfoContextDO vehicleRunTimeContext = actionContext.getVehicleRunTimeContext();
            if (vehicleRunTimeContext == null) {
                log.error("Friday Context Classify - 车辆运行时上下文为空");
                return "# 目前已知车辆信息\n- 车辆运行时上下文为空";
            }

            // 将车辆运行时信息对象转换为JSON字符串
            String vehicleRuntimeInfoSnapshot = JacksonUtils.to(vehicleRunTimeContext);
            if (StringUtils.isBlank(vehicleRuntimeInfoSnapshot)) {
                log.error("Friday Context Classify - 车辆运行时信息序列化失败: {}", vehicleRuntimeInfoSnapshot);
                return "# 目前已知车辆信息\n- 车辆运行时信息序列化失败";
            }

            // 使用工具类提取车辆信息，传入配置的模板
            return VehicleContextExtractor.extractVehicleInfo(
                    vehicleRuntimeInfoSnapshot,
                    sftFridayContextClassifyConfigDTO.getVehicleInfoTemplate()
            );
        } catch (Exception e) {
            log.error("Friday Context Classify - 提取车辆上下文信息失败", e);
            return "# 目前已知车辆信息\n- 车辆上下文信息提取失败：" + e.getMessage();
        }
    }

    /**
     * 验证SFT上下文分类结果
     *
     * @param requestDTO 请求参数
     * @param vehicleContext 车辆上下文信息
     * @return SFT上下文分类结果
     */
    private SFTFridayContextClassifyResultVTO verifySFTContextResult(FridayModelParamVTO requestDTO, String vehicleContext) {
        CheckUtil.isNotNull(requestDTO, "请求参数不能为空");
        String vin = requestDTO.getVin();
        Date occurTime = requestDTO.getOccurTime();
        Date checkStartTime = new Date();

        // 构造信息体
        List<FridayOpenAiAdapter.ReqMessage> messages = new ArrayList<>();

        // 添加系统信息
        FridayOpenAiAdapter.Content systemContent = FridayOpenAiAdapter.Content.builder()
                .type("text")
                .text(sftFridayContextClassifyConfigDTO.getSystemPrompt())
                .build();
        messages.add(FridayOpenAiAdapter.ReqMessage.builder()
                .role("system")
                .content(Lists.newArrayList(systemContent))
                .build());

        // 添加用户信息（包含车辆上下文）
        List<FridayOpenAiAdapter.Content> userContentList = new ArrayList<>();
        
        // 生成包含车辆上下文的用户提示词
        String userPromptWithContext = sftFridayContextClassifyConfigDTO.buildUserPromptWithContext(vehicleContext);
        FridayOpenAiAdapter.Content userPromptText = FridayOpenAiAdapter.Content.builder()
                .type("text")
                .text(userPromptWithContext)
                .build();
        userContentList.add(userPromptText);

        // 添加图像信息
        List<String> imageUrls = fridayOpenAiAdapter.buildImageContext(
                userContentList, 
                vin, 
                occurTime, 
                sftFridayContextClassifyConfigDTO.getViewPoints()
        );
        messages.add(FridayOpenAiAdapter.ReqMessage.builder()
                .role("user")
                .content(userContentList)
                .build());

        // 构建OpenAI请求
        FridayOpenAiAdapter.OpenAiReqDTO openAIReqDTO = fridayOpenAiAdapter.buildOpenAiAnswerReqDTO(messages, requestDTO.getModelName());

        // 获取OpenAI结果
        SFTFridayContextClassifyResultVTO contextClassifyResult = new SFTFridayContextClassifyResultVTO();

        // 单次请求获取结果
        FridayOpenAiAdapter.OpenAiRespDTO openAIAnswer = fridayOpenAiAdapter.getOpenAiAnswer(
                openAIReqDTO,
                requestDTO.getAppId(),
                requestDTO.getTimeout()
        );

        // 构造返回值
        SFTFridayContextClassifyResultVTOItem thisResultVTO = SFTFridayContextClassifyResultVTOItem.builder()
                .checkStartTime(checkStartTime)
                .imageUrls(imageUrls)
                .build();

        buildSFTFridayContextClassifyResult(thisResultVTO, openAIAnswer);
        log.info("Friday Context Classify - imageUrls : {}, result = {} ", imageUrls, thisResultVTO);

        // 设置结果
        contextClassifyResult.setData(Lists.newArrayList(thisResultVTO));
        contextClassifyResult.setFinalPredict(thisResultVTO);

        return contextClassifyResult;
    }

    /**
     * 构建 SFT Friday 上下文分类结果
     *
     * @param contextClassifyResultItem 结果项
     * @param openAiRespDTO OpenAI响应
     */
    public void buildSFTFridayContextClassifyResult(SFTFridayContextClassifyResultVTOItem contextClassifyResultItem,
                                                   FridayOpenAiAdapter.OpenAiRespDTO openAiRespDTO) {
        if (openAiRespDTO == null) {
            log.error("Friday Context Classify - OpenAI响应为空，直接返回");
            return;
        }

        if (CollectionUtils.isEmpty(openAiRespDTO.getChoices())) {
            log.error("Friday Context Classify - OpenAI响应无Choice，直接返回");
            return;
        }

        String content = extractContentFromResponse(openAiRespDTO);
        if (StringUtils.isEmpty(content)) {
            log.error("Friday Context Classify - 模型未给出判断结果，直接返回");
            return;
        }

        SFTParseResult parseResult = parseSFTContent(content);
        if (parseResult == null) {
            log.error("Friday Context Classify - 无法解析SFT内容: {}", content);
            return;
        }

        setSFTContextResult(contextClassifyResultItem, parseResult);
    }

    /**
     * 从OpenAI响应中提取内容
     *
     * @param openAiRespDTO OpenAI响应
     * @return 提取的内容
     */
    private String extractContentFromResponse(FridayOpenAiAdapter.OpenAiRespDTO openAiRespDTO) {
        return openAiRespDTO.getChoices().stream()
                .findFirst()
                .map(FridayOpenAiAdapter.Choice::getMessage)
                .filter(Objects::nonNull)
                .map(FridayOpenAiAdapter.RespMessage::getContent)
                .orElse(null);
    }

    /**
     * SFT解析结果
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SFTParseResult {
        // 预测信息
        private String predictedCategory;
        private Boolean predictedNeedTakeOver;
        private String rawOutputContent;
    }

    /**
     * 解析SFT内容
     *
     * @param content 待解析的内容，格式如："类型 : 排队通行, 是否需要接管: 否"
     * @return 解析结果
     */
    private SFTParseResult parseSFTContent(String content) {
        // 直接解析content内容，只保留预测结果
        SFTParseResult.SFTParseResultBuilder builder = SFTParseResult.builder()
                .rawOutputContent(content);  // 将content作为预测结果内容

        // 使用正则表达式解析content中的分类和接管信息
        if (extractModelResultPattern != null) {
            Matcher matcher = extractModelResultPattern.matcher(content);
            if (matcher.find()) {
                String category = matcher.group(1).trim();
                String takeOverStr = matcher.group(2).trim();
                Boolean needTakeOver = parseTakeOverValue(takeOverStr);

                builder.predictedCategory(category)
                       .predictedNeedTakeOver(needTakeOver);

                log.info("Friday Context Classify - 解析成功: 分类={}, 接管={}", category, needTakeOver);
                return builder.build();
            }
        }

        log.error("Friday Context Classify - 无法解析SFT内容: {}", content);
        return null;
    }

    /**
     * 设置SFT上下文分类结果
     *
     * @param contextClassifyResultItem 结果对象
     * @param parseResult 解析结果
     */
    private void setSFTContextResult(SFTFridayContextClassifyResultVTOItem contextClassifyResultItem, SFTParseResult parseResult) {
        // 设置预测信息
        SFTFridayContextClassifyResultVTO.ClassifyInfo predictedInfo = SFTFridayContextClassifyResultVTO.ClassifyInfo.builder()
                .category(parseResult.getPredictedCategory())
                .shouldBeTakeOver(parseResult.getPredictedNeedTakeOver())
                .build();
        contextClassifyResultItem.setPredictedInfo(predictedInfo);

        // 基于预测的"是否接管"判断来确定最终分类
        ISCheckCategoryEnum categoryEnum = null;
        Boolean predictedNeedTakeOver = parseResult.getPredictedNeedTakeOver();
        String predictedCategory = parseResult.getPredictedCategory();

        // 精确匹配逻辑
        if (predictedNeedTakeOver != null) {
            if (predictedNeedTakeOver) {
                // 预测需要接管，判断类型是否在GOOD类（接管类）里面
                categoryEnum = tryMatchCategory(predictedCategory);

                // 验证匹配到的分类是否为GOOD类
                if (categoryEnum != null && ISCheckCategoryEnum.isGood(categoryEnum)) {
                    // 在GOOD类里面，返回具体的GOOD类型
                    log.info("Friday Context Classify - 预测需要接管，匹配到GOOD类型: {}", categoryEnum);
                } else {
                    // 不在GOOD类里面，返回CANT_FOUND_ANY
                    categoryEnum = ISCheckCategoryEnum.CANT_FOUND_ANY;
                    log.info("Friday Context Classify - 预测需要接管，但类型不在GOOD类中: {}", predictedCategory);
                }
            } else {
                // 预测不需要接管，判断类型是否在BAD类里面
                categoryEnum = tryMatchCategory(predictedCategory);

                // 验证匹配到的分类是否为BAD类
                if (categoryEnum != null && ISCheckCategoryEnum.isBad(categoryEnum)) {
                    // 在BAD类里面，返回具体的BAD类型
                    log.info("Friday Context Classify - 预测不需要接管，匹配到BAD类型: {}", categoryEnum);
                } else {
                    // 不在BAD类里面，返回CANT_FOUND_ANY
                    categoryEnum = ISCheckCategoryEnum.CANT_FOUND_ANY;
                    log.info("Friday Context Classify - 预测不需要接管，但类型不在BAD类中: {}", predictedCategory);
                }
            }
        } else {
            // 接管状态没有预测出来，返回CANT_FOUND_ANY
            categoryEnum = ISCheckCategoryEnum.CANT_FOUND_ANY;
            log.info("Friday Context Classify - 接管状态未预测出来");
        }

        contextClassifyResultItem.setCheckCategory(categoryEnum);
        log.info("Friday Context Classify - 预测接管状态: {}, 预测分类: {}, 最终分类: {}",
                predictedNeedTakeOver, predictedCategory, categoryEnum);
    }

    /**
     * 解析是否需要接管的值
     *
     * @param takeOverStr 接管字符串
     * @return 是否需要接管
     */
    private Boolean parseTakeOverValue(String takeOverStr) {
        FridayOpenAiAdapter.SFTParseConfig config = getSFTParseConfig();

        if (config.getTakeOverValue().equals(takeOverStr)) {
            return true;
        } else if (config.getNotTakeOverValue().equals(takeOverStr)) {
            return false;
        }

        // 无法识别接管值，返回null，后续会设置为CANT_FOUND_ANY
        log.error("Friday Context Classify - 无法识别的接管值: {}, 将返回CANT_FOUND_ANY", takeOverStr);
        return null;
    }

    /**
     * 获取SFT解析配置
     *
     * @return 解析配置
     */
    private FridayOpenAiAdapter.SFTParseConfig getSFTParseConfig() {
        return FridayOpenAiAdapter.SFTParseConfig.builder()
                .takeOverValue(sftFridayContextClassifyConfigDTO.getTakeOverValue())
                .notTakeOverValue(sftFridayContextClassifyConfigDTO.getNotTakeOverValue())
                .defaultTakeOverCategory(sftFridayContextClassifyConfigDTO.getDefaultTakeOverCategory())
                .defaultNotTakeOverCategory(sftFridayContextClassifyConfigDTO.getDefaultNotTakeOverCategory())
                .build();
    }

    /**
     * 尝试匹配分类
     *
     * @param category 大模型推断出来的中文分类名称
     * @return 匹配的分类枚举，如果没有匹配则返回null
     */
    private ISCheckCategoryEnum tryMatchCategory(String category) {
        if (category == null || category.trim().isEmpty()) {
            return null;
        }

        // 1. 先尝试通过alias2category映射表查找（中文 → 英文代码）
        if (sftFridayContextClassifyConfigDTO.getAlias2category() != null) {
            String mappedCategory = sftFridayContextClassifyConfigDTO.getAlias2category().get(category);
            if (mappedCategory != null) {
                ISCheckCategoryEnum result = ISCheckCategoryEnum.getBySubcategory(mappedCategory);
                if (result != null) {
                    log.info("Friday Context Classify - 通过映射表匹配: {} → {} → {}", category, mappedCategory, result);
                    return result;
                }
            }
        }

        // 2. 映射表中没有，尝试通过name直接匹配（中文名称）
        ISCheckCategoryEnum result = ISCheckCategoryEnum.getByName(category);
        if (result != null) {
            log.info("Friday Context Classify - 通过name直接匹配: {} → {}", category, result);
            return result;
        }

        // 3. 最后尝试通过subcategory匹配（英文代码）
        result = ISCheckCategoryEnum.getBySubcategory(category);
        if (result != null) {
            log.info("Friday Context Classify - 通过subcategory匹配: {} → {}", category, result);
            return result;
        }

        log.info("Friday Context Classify - 无法匹配分类: {}", category);
        return null;
    }
}
