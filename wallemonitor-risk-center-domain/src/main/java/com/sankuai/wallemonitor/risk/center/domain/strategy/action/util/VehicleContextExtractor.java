package com.sankuai.wallemonitor.risk.center.domain.strategy.action.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.walleeve.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 车辆上下文信息提取工具类
 * 将车辆运行时信息转换为格式化的上下文字符串，用于AI模型分析
 * 
 */
@Slf4j
public class VehicleContextExtractor {

    // 驾驶模式映射
    private static final Map<String, String> DRIVE_MODE_MAP = new HashMap<String, String>() {{
        put("UNKNOWN", "未知");
        put("AUTONOMOUS_DRIVING", "自动驾驶");
        put("CLOUD_SOFT_TAKEOVER", "云控软接管状态");
        put("CLOUD_HARD_TAKEOVER", "云控硬接管状态");
        put("MANUAL_CONTROL", "手动控制状态");
        put("NO_CONTROL", "无控制状态");
    }};

    // 红绿灯状态映射
    private static final Map<String, String> TRAFFIC_LIGHT_MAP = new HashMap<String, String>() {{
        put("RED", "红");
        put("GREEN", "绿");
        put("YELLOW", "黄");
        put("NONE", "无红绿灯");
    }};

    // 交通线类型映射
    private static final Map<String, String> TRAFFIC_LINE_MAP = new HashMap<String, String>() {{
        put("YELLOW_SOLID_LINE", "在黄线上行驶");
        put("WHITE_SOLID_LINE", "在白线上行驶");
        put("NONE", "没有在交通线行驶");
    }};

    // 车辆类型长度映射
    private static final Map<String, String> VEHICLE_TYPE_LENGTH_MAP = new HashMap<String, String>() {{
        put("H24", "2m");
        put("S20", "2.5m");
    }};

    // 车道位置映射
    private static final Map<String, String> LANE_SECTION_MAP = new HashMap<String, String>() {{
        put("left", "车辆位于左边车道");
        put("middle", "车辆位于中间车道");
        put("right", "车辆位于右边车道");
    }};

    private static final String UNKNOWN_VALUE = "（未知，暂不考虑）";


    /**
     * 从车辆运行时信息JSON字符串中提取并格式化车辆上下文信息
     *
     * @param vehicleDataStr 车辆运行时信息的JSON字符串
     * @param template 车辆信息模板，如果为null则使用默认格式
     * @return 格式化的车辆上下文信息字符串
     */
    public static String extractVehicleInfo(String vehicleDataStr, String template) {
        if (StringUtils.isBlank(vehicleDataStr)) {
            return "# 目前已知车辆信息\n- 数据解析失败：输入为空";
        }

        try {
            Map<String, Object> vehicleData = JacksonUtils.from(vehicleDataStr, new TypeReference<Map<String, Object>>() {});
            return buildVehicleInfoString(vehicleData, template);
        } catch (Exception e) {
            log.error("解析车辆数据失败: {}", e.getMessage());
            return "# 目前已知车辆信息\n- 数据解析失败：JSON格式错误";
        }
    }

    /**
     * 构建车辆信息字符串
     */
    private static String buildVehicleInfoString(Map<String, Object> vehicleData, String template) {
        // 规则1: 车辆驾驶状态
        String driveMode = extractDriveMode(vehicleData);
        
        // 规则2: 车辆行驶速度
        String speed = extractSpeed(vehicleData);
        
        // 规则3: 红绿灯状态
        String trafficLightDesc = extractTrafficLightDesc(vehicleData);
        
        // 规则4: 前方路口距离
        String distanceToJunction = extractDistanceToJunction(vehicleData);
        
        // 规则5: 车辆行驶方向
        String direction = extractDirection(vehicleData);
        
        // 规则6: 车辆在交通线上行驶情况
        String trafficLine = extractTrafficLine(vehicleData);
        
        // 规则7: 车辆停滞时间
        String stagnationDuration = extractStagnationDuration(vehicleData);
        
        // 规则8: 距离施工区域的距离
        String constructionDesc = extractConstructionDesc(vehicleData);
        
        // 规则9: 是否有可借车道
        String hasUsableLane = extractHasUsableLane(vehicleData);
        
        // 规则10: 是否在单车道
        String isSingleLane = extractIsSingleLane(vehicleData);
        
        // 规则11: 车辆位置
        String vehiclePosition = extractVehiclePosition(vehicleData);
        
        // 规则12: 车辆距离左右路边距离
        String curbDistanceDesc = extractCurbDistanceDesc(vehicleData);
        
        // 规则13: 车辆周围车道信息
        String aroundLaneDesc = extractAroundLaneDesc(vehicleData);
        
        // 规则14: 车辆前1秒距离现在的距离
        String preVehicleDistance = extractPreVehicleDistance(vehicleData);
        
        // 规则15: 车辆周围障碍物信息
        String obstacleDesc = extractObstacleDesc(vehicleData);

        // 如果提供了模板，使用模板格式化；否则使用默认格式
            return template
                    .replace("{driveMode}", driveMode)
                    .replace("{speed}", speed)
                    .replace("{trafficLightDesc}", trafficLightDesc)
                    .replace("{distanceToJunction}", distanceToJunction)
                    .replace("{direction}", direction)
                    .replace("{trafficLine}", trafficLine)
                    .replace("{stagnationDuration}", stagnationDuration)
                    .replace("{constructionDesc}", constructionDesc)
                    .replace("{hasUsableLane}", hasUsableLane)
                    .replace("{isSingleLane}", isSingleLane)
                    .replace("{vehiclePosition}", vehiclePosition)
                    .replace("{curbDistanceDesc}", curbDistanceDesc)
                    .replace("{aroundLaneDesc}", aroundLaneDesc)
                    .replace("{preVehicleDistance}", preVehicleDistance)
                    .replace("{obstacleDesc}", obstacleDesc);
    }
      

    /**
     * 提取驾驶模式
     */
    private static String extractDriveMode(Map<String, Object> vehicleData) {
        String driveModeRaw = (String) vehicleData.get("driveMode");
        return DRIVE_MODE_MAP.getOrDefault(driveModeRaw, UNKNOWN_VALUE);
    }

    /**
     * 提取车辆速度
     */
    private static String extractSpeed(Map<String, Object> vehicleData) {
        Object speedObj = vehicleData.get("speed");
        if (speedObj == null) {
            return UNKNOWN_VALUE;
        }
        return String.valueOf(speedObj);
    }

    /**
     * 提取红绿灯描述
     */
    private static String extractTrafficLightDesc(Map<String, Object> vehicleData) {
        String trafficLightRaw = (String) vehicleData.get("trafficLightType");
        String trafficLight = TRAFFIC_LIGHT_MAP.getOrDefault(trafficLightRaw, UNKNOWN_VALUE);
        
        if (UNKNOWN_VALUE.equals(trafficLight)) {
            return UNKNOWN_VALUE;
        }

        // 获取红绿灯持续时间
        Map<String, Object> vehicleCounterInfo = getNestedMap(vehicleData, "vehicleCounterInfo");
        if (vehicleCounterInfo == null) {
            return "前方" + trafficLight + "灯";
        }

        Integer duration = null;
        if ("绿".equals(trafficLight)) {
            Map<String, Object> greenLightCounter = getNestedMap(vehicleCounterInfo, "greenLightCounter");
            if (greenLightCounter != null) {
                duration = (Integer) greenLightCounter.get("duration");
            }
        } else if ("红".equals(trafficLight)) {
            Map<String, Object> redLightCounter = getNestedMap(vehicleCounterInfo, "redLightCounter");
            if (redLightCounter != null) {
                duration = (Integer) redLightCounter.get("duration");
            }
        }

        if (duration != null) {
            return String.format("前方%s灯，持续%d秒", trafficLight, duration);
        } else if ("无红绿灯".equals(trafficLight)) {
            return "前方无红绿灯";
        } else {
            return "前方" + trafficLight + "灯";
        }
    }

    /**
     * 提取前方路口距离
     */
    private static String extractDistanceToJunction(Map<String, Object> vehicleData) {
        Object distanceObj = vehicleData.get("distanceToJunction");
        if (distanceObj == null) {
            return UNKNOWN_VALUE;
        }
        
        if (distanceObj instanceof Number) {
            int distance = ((Number) distanceObj).intValue();
            if (distance == -1) {
                return "前方无路口";
            } else {
                return distance + "m";
            }
        }
        
        return UNKNOWN_VALUE;
    }

    /**
     * 提取行驶方向
     */
    private static String extractDirection(Map<String, Object> vehicleData) {
        Boolean oppositeWithRoad = (Boolean) vehicleData.get("oppositeWithRoad");
        if (oppositeWithRoad == null) {
            return UNKNOWN_VALUE;
        }
        return oppositeWithRoad ? "逆行" : "正常行驶";
    }

    /**
     * 提取交通线行驶情况
     */
    private static String extractTrafficLine(Map<String, Object> vehicleData) {
        String trafficLineRaw = (String) vehicleData.get("drivingOnTrafficLineType");
        return TRAFFIC_LINE_MAP.getOrDefault(trafficLineRaw, UNKNOWN_VALUE);
    }

    /**
     * 提取停滞时间
     */
    private static String extractStagnationDuration(Map<String, Object> vehicleData) {
        Map<String, Object> vehicleCounterInfo = getNestedMap(vehicleData, "vehicleCounterInfo");
        if (vehicleCounterInfo == null) {
            return UNKNOWN_VALUE;
        }
        
        Map<String, Object> stagnationCounter = getNestedMap(vehicleCounterInfo, "stagnationCounter");
        if (stagnationCounter == null) {
            return UNKNOWN_VALUE;
        }
        
        Object duration = stagnationCounter.get("duration");
        return duration != null ? String.valueOf(duration) : UNKNOWN_VALUE;
    }

    /**
     * 提取施工区域距离描述
     */
    private static String extractConstructionDesc(Map<String, Object> vehicleData) {
        Object constructionDistanceObj = vehicleData.get("distanceToFrontConstructionZone");
        if (constructionDistanceObj == null) {
            return UNKNOWN_VALUE;
        }
        
        if (constructionDistanceObj instanceof Number) {
            int distance = ((Number) constructionDistanceObj).intValue();
            if (distance == -1) {
                return "附近没有施工区域";
            } else {
                return distance + "m";
            }
        }
        
        return UNKNOWN_VALUE;
    }

    /**
     * 提取是否有可借车道
     */
    private static String extractHasUsableLane(Map<String, Object> vehicleData) {
        Object usableLaneIdsObj = vehicleData.get("usableLaneIds");
        if (usableLaneIdsObj == null) {
            return UNKNOWN_VALUE;
        }
        
        if (usableLaneIdsObj instanceof List) {
            List<?> usableLaneIds = (List<?>) usableLaneIdsObj;
            return usableLaneIds.isEmpty() ? "无" : "有";
        }
        
        return UNKNOWN_VALUE;
    }

    /**
     * 提取是否在单车道
     */
    private static String extractIsSingleLane(Map<String, Object> vehicleData) {
        Boolean singleLane = (Boolean) vehicleData.get("singleLane");
        if (singleLane == null) {
            return UNKNOWN_VALUE;
        }
        return singleLane ? "是" : "否";
    }

    /**
     * 提取车辆位置
     */
    private static String extractVehiclePosition(Map<String, Object> vehicleData) {
        String vehiclePositionRaw = (String) vehicleData.get("vehicleLaneSectionType");
        return LANE_SECTION_MAP.getOrDefault(vehiclePositionRaw, UNKNOWN_VALUE);
    }

    /**
     * 提取车辆距离左右路边距离描述
     */
    private static String extractCurbDistanceDesc(Map<String, Object> vehicleData) {
        Object distanceListObj = vehicleData.get("distanceToNearCurbList");
        if (!(distanceListObj instanceof List)) {
            return UNKNOWN_VALUE;
        }

        List<?> distanceList = (List<?>) distanceListObj;
        if (distanceList.size() < 2) {
            return UNKNOWN_VALUE;
        }

        try {
            double leftDistance = ((Number) distanceList.get(0)).doubleValue();
            double rightDistance = ((Number) distanceList.get(1)).doubleValue();
            return String.format("距离左边%.1fm，距离右边%.1fm", leftDistance, rightDistance);
        } catch (Exception e) {
            return UNKNOWN_VALUE;
        }
    }

    /**
     * 提取车辆周围车道信息
     */
    private static String extractAroundLaneDesc(Map<String, Object> vehicleData) {
        Map<String, Object> aroundLane = getNestedMap(vehicleData, "vehicleAroundLaneId");
        if (aroundLane == null) {
            return UNKNOWN_VALUE;
        }

        String successor = Boolean.TRUE.equals(aroundLane.get("successor")) ? "有车道" : "无车道";
        String left = Boolean.TRUE.equals(aroundLane.get("left")) ? "有车道" : "无车道";
        String right = Boolean.TRUE.equals(aroundLane.get("right")) ? "有车道" : "无车道";
        String predecessor = Boolean.TRUE.equals(aroundLane.get("predecessor")) ? "有车道" : "无车道";

        return String.format("车辆前方%s，左边%s，后方%s，右边%s", successor, left, predecessor, right);
    }

    /**
     * 提取车辆前1秒距离现在的距离
     */
    private static String extractPreVehicleDistance(Map<String, Object> vehicleData) {
        Map<String, Object> preVehiclePosition = getNestedMap(vehicleData, "preVehiclePosition");
        if (preVehiclePosition == null) {
            return UNKNOWN_VALUE;
        }

        Object distance = preVehiclePosition.get("distance");
        return distance != null ? String.valueOf(distance) : UNKNOWN_VALUE;
    }

    /**
     * 提取车辆周围障碍物信息
     */
    private static String extractObstacleDesc(Map<String, Object> vehicleData) {
        Object allObstacleListObj = vehicleData.get("allObstacleList");
        if (!(allObstacleListObj instanceof List)) {
            return "周围有" + UNKNOWN_VALUE + "个障碍物";
        }

        List<?> allObstacles = (List<?>) allObstacleListObj;
        int totalObstacles = allObstacles.size();

        if (totalObstacles == 0) {
            return "周围有" + UNKNOWN_VALUE + "个障碍物";
        }

        StringBuilder obstacleDesc = new StringBuilder();
        obstacleDesc.append("周围有").append(totalObstacles).append("个障碍物");

        // 分析前方路障
        String frontBarrierDesc = extractFrontBarrierDesc(allObstacles);
        obstacleDesc.append("，").append(frontBarrierDesc);

        // 分析前方障碍物
        String frontObstacleDesc = extractFrontObstacleDesc(vehicleData);
        obstacleDesc.append("，").append(frontObstacleDesc);

        // 分析左边障碍物
        String leftObstacleDesc = extractLeftObstacleDesc(allObstacles);
        obstacleDesc.append("，").append(leftObstacleDesc);

        // 分析后方障碍物
        String behindObstacleDesc = extractBehindObstacleDesc(vehicleData);
        obstacleDesc.append("，").append(behindObstacleDesc);

        // 分析右边障碍物
        String rightObstacleDesc = extractRightObstacleDesc(allObstacles);
        obstacleDesc.append("，").append(rightObstacleDesc);

        return obstacleDesc.toString();
    }

    /**
     * 提取前方路障描述
     */
    private static String extractFrontBarrierDesc(List<?> allObstacles) {
        Map<String, Object> nearestBarrier = null;
        double minDistance = Double.MAX_VALUE;

        for (Object obstacleObj : allObstacles) {
            if (!(obstacleObj instanceof Map)) continue;

            @SuppressWarnings("unchecked")
            Map<String, Object> obstacle = (Map<String, Object>) obstacleObj;

            String fineType = (String) obstacle.get("fineType");
            Object middleAngleObj = obstacle.get("middleAngle");

            if ("BARRIER".equals(fineType) && middleAngleObj instanceof Number) {
                double middleAngle = ((Number) middleAngleObj).doubleValue();
                if (middleAngle < 60) {
                    Object distanceObj = obstacle.get("distance");
                    if (distanceObj instanceof Number) {
                        double distance = ((Number) distanceObj).doubleValue();
                        if (distance < minDistance) {
                            minDistance = distance;
                            nearestBarrier = obstacle;
                        }
                    }
                }
            }
        }

        if (nearestBarrier != null) {
            return String.format("前方最近路障为：路障距离%.1fm", minDistance);
        } else {
            return "前方最近路障为：无";
        }
    }

    /**
     * 提取前方障碍物描述
     */
    private static String extractFrontObstacleDesc(Map<String, Object> vehicleData) {
        Object frontObstacleListObj = vehicleData.get("frontObstacleList");
        if (!(frontObstacleListObj instanceof List)) {
            return "前方最近障碍物为：无";
        }

        List<?> frontObstacles = (List<?>) frontObstacleListObj;
        if (frontObstacles.isEmpty()) {
            return "前方最近障碍物为：无";
        }

        Map<String, Object> nearestFront = null;
        double minDistance = Double.MAX_VALUE;

        for (Object obstacleObj : frontObstacles) {
            if (!(obstacleObj instanceof Map)) continue;

            @SuppressWarnings("unchecked")
            Map<String, Object> obstacle = (Map<String, Object>) obstacleObj;

            Object distanceObj = obstacle.get("distance");
            if (distanceObj instanceof Number) {
                double distance = ((Number) distanceObj).doubleValue();
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestFront = obstacle;
                }
            }
        }

        if (nearestFront != null) {
            String frontType = Optional.ofNullable((String) nearestFront.get("type")).orElse("未知");
            return String.format("前方最近障碍物为：%s距离%.1fm", frontType, minDistance);
        } else {
            return "前方最近障碍物为：无";
        }
    }

    /**
     * 提取左边障碍物描述
     */
    private static String extractLeftObstacleDesc(List<?> allObstacles) {
        return extractSideObstacleDesc(allObstacles, "left", "左边最近障碍物为");
    }

    /**
     * 提取右边障碍物描述
     */
    private static String extractRightObstacleDesc(List<?> allObstacles) {
        return extractSideObstacleDesc(allObstacles, "right", "右边最近障碍物为");
    }

    /**
     * 提取侧边障碍物描述的通用方法
     */
    private static String extractSideObstacleDesc(List<?> allObstacles, String side, String prefix) {
        Map<String, Object> nearestVehicle = null;
        double minDistance = Double.MAX_VALUE;

        for (Object obstacleObj : allObstacles) {
            if (!(obstacleObj instanceof Map)) continue;

            @SuppressWarnings("unchecked")
            Map<String, Object> obstacle = (Map<String, Object>) obstacleObj;

            String type = (String) obstacle.get("type");
            String laneRelation = (String) obstacle.get("laneRelation2Vehicle");

            if ("VEHICLE".equals(type) && laneRelation != null && laneRelation.contains(side)) {
                Object distanceObj = obstacle.get("distance");
                if (distanceObj instanceof Number) {
                    double distance = ((Number) distanceObj).doubleValue();
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestVehicle = obstacle;
                    }
                }
            }
        }

        if (nearestVehicle != null) {
            return String.format("%s：汽车距离%.1fm", prefix, minDistance);
        } else {
            return prefix + "：无";
        }
    }

    /**
     * 提取后方障碍物描述
     */
    private static String extractBehindObstacleDesc(Map<String, Object> vehicleData) {
        Object behindObstacleListObj = vehicleData.get("behindObstacleList");
        if (!(behindObstacleListObj instanceof List)) {
            return "后方最近障碍物为：无";
        }

        List<?> behindObstacles = (List<?>) behindObstacleListObj;
        if (behindObstacles.isEmpty()) {
            return "后方最近障碍物为：无";
        }

        Map<String, Object> nearestBehind = null;
        double minDistance = Double.MAX_VALUE;

        for (Object obstacleObj : behindObstacles) {
            if (!(obstacleObj instanceof Map)) continue;

            @SuppressWarnings("unchecked")
            Map<String, Object> obstacle = (Map<String, Object>) obstacleObj;

            Object distanceObj = obstacle.get("distance");
            if (distanceObj instanceof Number) {
                double distance = ((Number) distanceObj).doubleValue();
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestBehind = obstacle;
                }
            }
        }

        if (nearestBehind != null) {
            String behindType = Optional.ofNullable((String) nearestBehind.get("type")).orElse("未知");
            return String.format("后方最近障碍物为：%s距离%.1fm", behindType, minDistance);
        } else {
            return "后方最近障碍物为：无";
        }
    }

    /**
     * 安全获取嵌套Map
     */
    @SuppressWarnings("unchecked")
    private static Map<String, Object> getNestedMap(Map<String, Object> parent, String key) {
        Object obj = parent.get(key);
        if (obj instanceof Map) {
            return (Map<String, Object>) obj;
        }
        return null;
    }
}
