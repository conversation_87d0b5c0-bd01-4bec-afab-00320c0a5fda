package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.response.VehicleDataInfoVO;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.VehicleLocationUpdatedProcess;
import com.sankuai.wallemonitor.risk.center.domain.result.ISWaitingInQueueResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISPreHandleDataAction;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISRandomForestClassify;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISRandomForestClassifyV2;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISSwitchPower;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISWaitingInQueueV7;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleObstacleInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.PreHandleDataConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.Position;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GeoQueryDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoLocationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeLocationDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.ClassPathResource;
import org.junit.Assert;
import java.util.Arrays;

public class ISInWaitingInQueueV7Test extends SpringTestBase {

    @MockBean
    private VehicleAdapter vehicleAdapter;

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private VehicleRuntimeInfoContextRepository contextRepository;

    @Resource
    private ISWaitingInQueueV7 isInQueue;

    @Resource
    private ISSwitchPower isSwitchPower;

    @Resource
    private ISRandomForestClassify isRandomForestClassify;

    @Resource
    private ISRandomForestClassifyV2 isRandomForestClassifyV2;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleRuntimeInfoLocationRepository locationRepository;

    @Resource
    private VehicleLocationUpdatedProcess vehicleLocationUpdatedProcess;

    @Resource
    private ISPreHandleDataAction preHandleDataAction;

    @Resource
    private ISWaitingInQueueV7 v7;

    @Before
    @SneakyThrows
    public void init() {
        String perceptionObstacleDTOStr = "{\"arbitrationMonitorContext\":{\"noiseCategory\":\"NOISE_CATEGORY_LOW\"},\"errorCode\":\"OK\",\"perceptionObstacle\":[{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.01565505911712093,\"y\":0.9998774520529039,\"z\":0},\"displayBox\":{},\"height\":0.0005221366882324219,\"id\":4770312,\"length\":0.2864589130049797,\"obstacleType\":{\"coarseType\":\"CAR\",\"fineType\":\"FINE_CAR\"},\"position\":{\"x\":461256.93584242987,\"y\":4437531.220559789,\"z\":21.57869356555014},\"theta\":1.5551406276576785,\"trackingTime\":0,\"type\":\"VEHICLE\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.06872160272159533}],\"pose\":{}}";
        //
        PerceptionObstacleDTO perceptionObstacleDTO = JacksonUtils.from(perceptionObstacleDTOStr,
                PerceptionObstacleDTO.class);
        // 116.5679645969, 39.7816047202
        PositionDO perceptionObstaclePosition = GeoToolsUtil.wgs84ToUtm(116.5679645969, 39.7816047202);
        perceptionObstacleDTO.getPerceptionObstacle().get(0).setPosition(Position.builder()
                .x(perceptionObstaclePosition.getLongitude()).y(perceptionObstaclePosition.getLatitude()).build());
        VehicleRuntimeInfoContextDO contextDO = contextRepository.getFromCache("LMTZSV029MC063825");
        contextDO.updateObstacle(perceptionObstacleDTO, 100.0);
        contextDO.setLat("39.7816212639");
        contextDO.setLng("116.5679663187");
        contextDO.setDistanceToJunction(-1D);
        contextDO.setLastUpdateTime(new Date());
        contextRepository.updateCache(contextDO, contextDO.getLastUpdateTime().getTime());
        contextRepository.save(contextDO);
        DomainEventDTO<VehicleRuntimeInfoContextDO> eventDTO = new DomainEventDTO<>();
        eventDTO.setBefore(new ArrayList<>());
        eventDTO.setAfter(Collections.singletonList(contextDO));
        eventDTO.setTimestamp(new Date().getTime());
        eventDTO.setTraceId("123");
        vehicleLocationUpdatedProcess
                .process(DomainEventFactory.createDomainEventChangeDTO(eventDTO, VehicleRuntimeInfoContextDO.class));
        List<VehicleDataInfoVO> vehicleDataInfoVOList = new ArrayList<>();
        VehicleEveInfoVTO vehicleEveInfoVTO = new VehicleEveInfoVTO();
        VehicleDataInfoVO vehicleDataInfoVO = new VehicleDataInfoVO();
        vehicleDataInfoVO.setVin("LMTZSV029MC063825");
        vehicleDataInfoVO.setLatitude(39.7816404316);
        vehicleDataInfoVO.setLongitude(116.5679666916);
        vehicleDataInfoVOList.add(vehicleDataInfoVO);
        Mockito.doReturn(vehicleDataInfoVOList).when(vehicleAdapter).queryVehicleHistoryDataFromEveReplay(Mockito.any(),
                Mockito.any(), Mockito.any());
        // "yizhuang_hdmap_v5.38.13.r"
        Mockito.doReturn("yizhuang").when(vehicleAdapter).getVehicleHdMapArea(Mockito.any());
        // 返回
        vehicleEveInfoVTO.setVehicleType("S20");
        vehicleEveInfoVTO.setVin("LMTZSV029MC063825");
        vehicleEveInfoVTO.setHdMapVersion("yizhuang");
        Mockito.doReturn(vehicleEveInfoVTO).when(vehicleAdapter).queryRuntimeVehicleInfoByVin(Mockito.any());
        System.out.println(JacksonUtils.to(locationRepository.queryByParam(VehicleRuntimeLocationDOQueryParamDTO
                .builder().vinList(Lists.newArrayList("LMTZSV029MC063825"))
                .locationQuery(GeoQueryDO.builder().distance(50D).point(contextDO.getLocation().toPoint()).build())
                .build())));
    }

    @Test
    public void testPreHandle() {
        PreHandleDataConfig preHandleDataConfig = lionConfigRepository.getPreHandleDataConfig();
        Map<String, Map<String, Object>> actionConfigMap = new HashMap<>();
        actionConfigMap.put("ISPreHandleDataAction", JacksonUtils.fromMap(JacksonUtils.to(preHandleDataConfig)));
        ISCheckActionContext context = ISCheckActionContext.builder()
                //
                .item(RiskCheckingQueueItemDO.builder().occurTime(new Date()).build()) //
                .vehicleRunTimeContext(contextRepository.getFullByVin("LMTZSV029MC063825")) //
                //
                .currentActionName("ISPreHandleDataAction")
                //
                .actionConfigMap(actionConfigMap).build();
        preHandleDataAction.execute(context);
        RiskAutoCheckConfigDTO riskAutoMarkConfigDTO = lionConfigRepository.getRiskAutoMarkConfigByVersion("");
        context.setCurrentActionName("ISWaitingInQueueV7");
        riskAutoMarkConfigDTO.getActionCommonConfig()
                .put("ISWaitingInQueueV7", riskAutoMarkConfigDTO.getActionCommonConfig().get("ISWaitingInQueueV5"));
        context.setActionConfigMap(riskAutoMarkConfigDTO.getActionCommonConfig());
        v7.execute(context);
    }
    @Test
    public void test() {
        // 第一个点 [116.5679666916, 39.7816404316],
        // 车的点 [116.5679663187, 39.7816212639],
        // 障碍物的点 [116.5679645969, 39.7816047202],
        // 1 = {PositionDO@5078} "PositionDO(latitude=39.7816404316, longitude=116.5679666916, coordinateSystem=WGS84)"
        // 2 = {PositionDO@5079} "PositionDO(latitude=39.7816212639, longitude=116.5679663187, coordinateSystem=WGS84)"
        // 3 = {PositionDO@5080} "PositionDO(latitude=39.7816047202, longitude=116.5679645969, coordinateSystem=WGS84)"
        // 4 = {PositionDO@5081} "PositionDO(latitude=39.7815866348, longitude=116.567961066, coordinateSystem=WGS84)"
        RiskAutoCheckConfigDTO riskAutoMarkConfigDTO = lionConfigRepository.getRiskAutoMarkConfigByVersion("");
        isInQueue.execute(
                ISCheckActionContext.builder()
                        //
                        .item(RiskCheckingQueueItemDO.builder().occurTime(new Date()).build()) //
                        .vehicleRunTimeContext(contextRepository.getFullByVin("LMTZSV029MC063825")) //
                        //
                        .currentActionName("ISWaitingInQueueV5")
                        //
                        .actionConfigMap(riskAutoMarkConfigDTO.getActionCommonConfig()).build());

    }

    @Test
    public void test1() {
        // String runtime =
        // "{\"vin\":\"LMTZSV026MC058873\",\"driveMode\":\"AUTONOMOUS_DRIVING\",\"speed\":0.0,\"lng\":\"114.380029\",\"lat\":\"22.725390\",\"batterySwitching\":false,\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"trafficLightType\":\"UNKNOWN\",\"distanceToNextJunction\":15,\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"waitingGatePole\":false,\"obstacleContext\":{\"perceptionObstacle\":[{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.3760593434964016,\"y\":-0.9265955806950449,\"z\":0.0},\"height\":3.1625137329101562,\"id\":\"1461847\",\"length\":7.865609169006348,\"obstacleType\":{\"coarseType\":\"CAR\",\"fineType\":\"FINE_TRUCK\"},\"position\":{\"x\":230898.83449275364,\"y\":2515491.334782609,\"z\":54.52929863557958},\"theta\":-1.185256551241094,\"type\":\"VEHICLE\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":2.845954418182373},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.39548167121139627,\"y\":-0.9184738688346072,\"z\":0.0},\"height\":1.9684492349624634,\"id\":\"1462180\",\"length\":3.4767353534698486,\"obstacleType\":{\"coarseType\":\"CYCLIST\",\"fineType\":\"FINE_TRICYCLIST\"},\"position\":{\"x\":230894.69133709985,\"y\":2515489.0996233523,\"z\":54.30881082838223},\"theta\":-1.1642041110726111,\"type\":\"BICYCLE\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":1.6878196001052856},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":-0.991803754600299,\"y\":0.12777054572690358,\"z\":0.0},\"height\":0.6947802901268005,\"id\":\"1469014\",\"length\":0.5440123600722,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER\"},\"position\":{\"x\":230887.4902956236,\"y\":2515490.293410383,\"z\":53.84133831440339},\"theta\":3.0134718782466288,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.16531875513875605},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":1.0,\"y\":0.0,\"z\":0.0},\"height\":2.012466311454773,\"id\":\"1468071\",\"length\":0.39999999999417923,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":230884.5,\"y\":2515500.6,\"z\":54.45506886815092},\"theta\":0.0,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.599999999627471},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":-0.28849519379054783,\"y\":-0.9574813435033032,\"z\":0.0},\"height\":1.1996065974235535,\"id\":\"1468821\",\"length\":0.49581081001088023,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":230888.044649133,\"y\":2515491.611004179,\"z\":53.89609007632673},\"theta\":-1.8634511634002062,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.24573461920954287},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.4941426105592167,\"y\":-0.8693808603973353,\"z\":0.0},\"height\":2.032340705394745,\"id\":\"1468880\",\"length\":0.5810526213608682,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"516\"},\"position\":{\"x\":230886.9500000002,\"y\":2515493.950000001,\"z\":54.47201974390927},\"theta\":-1.0539479806629939,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.38167393184266984},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":6.123233995736757E-17,\"y\":1.0,\"z\":0.0},\"height\":2.028906911611557,\"id\":\"1470054\",\"length\":0.6000000000931323,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"516\"},\"position\":{\"x\":230883.80000000002,\"y\":2515502.0,\"z\":54.51582651687549},\"theta\":1.5707963267948966,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.20000000001164153},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.3672160075494459,\"y\":-0.9301356910666347,\"z\":0.0},\"height\":1.1139571964740753,\"id\":\"1475263\",\"length\":3.195910209789872,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":230887.7615372046,\"y\":2515489.5922940033,\"z\":53.8684416796528},\"theta\":-1.1947821863727697,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.4721323500853032},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":-0.3707993323126966,\"y\":0.9287130101121432,\"z\":0.0},\"height\":1.0227068066596985,\"id\":\"1475589\",\"length\":11.171848471742123,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":230900.12639230548,\"y\":2515493.2103155353,\"z\":53.82237472727701},\"theta\":1.950665889249484,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.3481449205428362},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.3603312022638541,\"y\":-0.9328244340017071,\"z\":0.0},\"height\":1.1625478267669678,\"id\":\"1475651\",\"length\":11.956392435822636,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":230896.00466975314,\"y\":2515504.040578152,\"z\":53.81384659012856},\"theta\":-1.2021734056820303,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.29863322572782636}]},\"fenceContext\":{\"fenceAndFieldMetas\":[{\"perceptionId\":\"1461847\",\"stopDistanceToEgo\":0.1999999999999993,\"position\":{\"x\":230895.44687267745,\"y\":2515498.853143989},\"constraintSourceType\":\"STATIONARY\"}]},\"lastUpdateTime\":\"2025-02-07
        // 14:56:28\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-02-07
        // 14:55:24\",\"duration\":64,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code
        // == 1\",\"startTime\":\"2025-02-07 14:55:18\",\"endTime\":\"2025-02-07
        // 14:55:24\",\"duration\":6,\"countFinished\":true},\"curTrafficLight\":{\"color\":\"UNKNOWN\",\"status\":\"NORMAL\",\"id\":\"s_a33f8b7f\",\"red\":false},\"createTime\":\"2024-10-19
        // 08:55:30\",\"updateTime\":\"2025-02-07
        // 14:56:28\",\"isDeleted\":\"NOT_DELETED\",\"location\":{\"latitude\":22.72539,\"longitude\":114.380029,\"coordinateSystem\":\"WGS84\"}}";
        // String positionHistoryList = "[{\"vin\":\"LMTZSV026MC058873\",\"time\":\"2025-02-07
        // 14:55:20\",\"longitude\":114.38002297064594,\"latitude\":22.725403042194298,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV026MC058873\",\"time\":\"2025-02-07
        // 14:55:21\",\"longitude\":114.38002387562584,\"latitude\":22.725400863726666,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV026MC058873\",\"time\":\"2025-02-07
        // 14:55:22\",\"longitude\":114.38002625413138,\"latitude\":22.725395270029143,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV026MC058873\",\"time\":\"2025-02-07
        // 14:55:23\",\"longitude\":114.3800284612848,\"latitude\":22.72539015732808,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV026MC058873\",\"time\":\"2025-02-07
        // 14:55:24\",\"longitude\":114.3800284612848,\"latitude\":22.72539015732808,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"}]";
        // String area = "shenzhenpingshan";
        // Scanner scanner = new Scanner(System.in);
        // while (true) {
        // 扫描一行输入
        // System.out.println("输入车辆上下文");
        String runtime = "{\"vin\":\"LMTZSV020MC092114\",\"driveMode\":\"AUTONOMOUS_DRIVING\",\"speed\":0.0,\"lng\":\"114.369817\",\"lat\":\"22.734455\",\"batterySwitching\":false,\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"trafficLightType\":\"UNKNOWN\",\"distanceToNextJunction\":62,\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"waitingGatePole\":false,\"obstacleContext\":{\"perceptionObstacle\":[{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.549023964305679,\"y\":-0.8358066083817556,\"z\":0.0},\"height\":3.221740961074829,\"id\":\"2029521\",\"length\":8.389266967773438,\"obstacleType\":{\"coarseType\":\"CAR\",\"fineType\":\"FINE_TRUCK\"},\"position\":{\"x\":229868.07176828934,\"y\":2516516.**********,\"z\":39.3903147115117},\"theta\":-0.9896003126731592,\"type\":\"VEHICLE\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":2.8795015811920166},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.585523216085217,\"y\":-0.8106556380002342,\"z\":0.0},\"height\":1.7670931816101074,\"id\":\"2034959\",\"length\":4.486067771911621,\"obstacleType\":{\"coarseType\":\"CAR\",\"fineType\":\"FINE_CAR\"},\"position\":{\"x\":229861.46726190476,\"y\":2516524.933730159,\"z\":38.823109661959634},\"theta\":-0.9452709951058481,\"type\":\"VEHICLE\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":2.013162851333618},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.9979130500629712,\"y\":-0.06457201029806528,\"z\":0.0},\"height\":2.0865045189857483,\"id\":\"2036546\",\"length\":0.31512586402823217,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"516\"},\"position\":{\"x\":229862.44999999995,\"y\":2516532.1500000013,\"z\":38.89548056521989},\"theta\":-0.06461696764372492,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.2115726307965815},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":-0.8566194882319452,\"y\":-0.5159486916152103,\"z\":0.0},\"height\":1.71157406270504,\"id\":\"2036705\",\"length\":0.3317823230754584,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":229872.60986750008,\"y\":2516520.3509922973,\"z\":39.20412548109524},\"theta\":-2.5994778914354133,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.1112583780195564},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.25819924450306053,\"y\":-0.9660916882665497,\"z\":0.0},\"height\":2.094706565141678,\"id\":\"2037725\",\"length\":0.4489550101570785,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"516\"},\"position\":{\"x\":229854.57131479634,\"y\":2516522.323051726,\"z\":38.84549611406379},\"theta\":-1.3096385480960313,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.28861127665732056},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":-0.5870213594226288,\"y\":-0.8095714443934963,\"z\":0.0},\"height\":1.2142652571201324,\"id\":\"2039027\",\"length\":0.1339531340636313,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"516\"},\"position\":{\"x\":229858.0,\"y\":2516513.8,\"z\":39.49666626659412},\"theta\":-2.198170955701474,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.08945602015592158},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.999974522601316,\"y\":-0.007138217058989236,\"z\":0.0},\"height\":1.9026117622852325,\"id\":\"2039122\",\"length\":0.26731166781974025,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":229861.22551121464,\"y\":2516511.8058699304,\"z\":39.26501702203529},\"theta\":-0.007138275909207693,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.2056673695333302},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":-0.15252539847357763,\"y\":-0.988299551157104,\"z\":0.0},\"height\":2.147934913635254,\"id\":\"2039132\",\"length\":0.17931940499693155,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"516\"},\"position\":{\"x\":229862.40212525165,\"y\":2516512.1936102957,\"z\":39.13785568254833},\"theta\":-1.7239193960339478,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.13435839948942885},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.572574103113512,\"y\":-0.8198529724542549,\"z\":0.0},\"height\":1.2336048185825348,\"id\":\"2050632\",\"length\":13.59349087625742,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":229873.58918809867,\"y\":2516518.2351231943,\"z\":38.74341131638596},\"theta\":-0.9611541893711112,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.6166699891909957},{\"acceleration\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"confidence\":1,\"direction\":{\"x\":0.6743598552054668,\"y\":-0.7384028613745433,\"z\":0.0},\"height\":1.2851732075214386,\"id\":\"2050723\",\"length\":12.931904492201284,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_BARRIER\"},\"position\":{\"x\":229865.1549482674,\"y\":2516528.5588639528,\"z\":38.4309567836513},\"theta\":-0.8306988987269958,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0.0,\"y\":0.0,\"z\":0.0},\"width\":0.39247262314893305}]},\"fenceContext\":{\"fenceAndFieldMetas\":[{\"perceptionId\":\"2029521\",\"position\":{\"x\":229863.82528864185,\"y\":2516521.8887800137},\"constraintSourceType\":\"STATIONARY\"}]},\"lastUpdateTime\":\"2025-02-10 16:16:40\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-02-10 16:15:06\",\"duration\":94,\"countFinished\":false},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-02-10 16:12:46\",\"endTime\":\"2025-02-10 16:13:46\",\"duration\":60,\"countFinished\":true},\"curTrafficLight\":{\"color\":\"UNKNOWN\",\"status\":\"NORMAL\",\"id\":\"s_34ac6641\",\"red\":false},\"createTime\":\"2024-10-18 20:51:53\",\"updateTime\":\"2025-02-10 16:16:40\",\"isDeleted\":\"NOT_DELETED\",\"location\":{\"latitude\":22.734455,\"longitude\":114.369817,\"coordinateSystem\":\"WGS84\",\"pointList\":[114.369817,22.734455]}}";
        // System.out.println("历史定位");
        String positionHistoryList = "[{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:14:57\",\"longitude\":114.36975475950469,\"latitude\":22.734537628080936,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:14:58\",\"longitude\":114.36976953932133,\"latitude\":22.734517712586783,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:14:59\",\"longitude\":114.36978011698818,\"latitude\":22.7345035034607,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:15:00\",\"longitude\":114.36980387822872,\"latitude\":22.734472463043534,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:15:01\",\"longitude\":114.36980387822872,\"latitude\":22.734472463043534,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:15:02\",\"longitude\":114.36981244489192,\"latitude\":22.734461344048317,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:15:03\",\"longitude\":114.36981726019279,\"latitude\":22.734455045408236,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:15:04\",\"longitude\":114.36981705057653,\"latitude\":22.73445536232156,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:15:05\",\"longitude\":114.36981705057653,\"latitude\":22.73445536232156,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV020MC092114\",\"time\":\"2025-02-10 16:15:06\",\"longitude\":114.36982,\"latitude\":22.734459,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"gnss_bestpos\"}]";
        // System.out.println("区域");
        // String obstacleListStr = getFromFile();
        String area = "shenzhenpingshan";
        VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = JacksonUtils.from(runtime,
                VehicleRuntimeInfoContextDO.class);
        List<VehicleDataInfoVO> eveThriftResponse = JacksonUtils.from(positionHistoryList,
                new TypeReference<List<VehicleDataInfoVO>>() {
                });
        Mockito.doReturn(eveThriftResponse).when(vehicleAdapter).queryVehicleHistoryDataFromEveReplay(Mockito.any(),
                Mockito.any(), Mockito.any());
        // "yizhuang_hdmap_v5.38.13.r"
        Mockito.doReturn(area).when(vehicleAdapter).getVehicleHdMapArea(Mockito.any());
        // vehicleRuntimeInfoContextDO.getObstacleContext().setPerceptionObstacle(perceptionObstacleList);
        contextRepository.updateCache(vehicleRuntimeInfoContextDO, new Date().getTime());
        contextRepository.save(vehicleRuntimeInfoContextDO);

        ISCheckActionResult<ISWaitingInQueueResult> checkActionResult = isInQueue.execute(
                ISCheckActionContext.builder().item(RiskCheckingQueueItemDO.builder().occurTime(new Date()).build())
                        .vehicleRunTimeContext(vehicleRuntimeInfoContextDO).build());
        ISWaitingInQueueResult result = checkActionResult.getActionResult();
        // 本车的坐标
        List<Double> curLocation = Optional.ofNullable(result.getVehiclePosition()).map(PositionDO::getPointList)
                .orElse(Collections.emptyList());

        // 本车的前序坐标
        List<Double> preLocation = Optional.ofNullable(result.getPreVehiclePosition())
                .map(VehicleInQueuePositionDTO::getPosition).map(PositionDO::getPointList)
                .orElse(Collections.emptyList());

        // 障碍物的坐标
        List<Double> obsLocation = Optional.ofNullable(result.getFrontObstacle())
                .map(VehicleObstacleInfoDTO::getPosition).map(PositionDO::getPointList)
                .orElse(Collections.emptyList());
        List<List<Double>> allObLocationList = Optional.of(result.getAllObstacleList())
                .orElse(new ArrayList<>()).stream().map(VehicleObstacleInfoDTO::getPosition)
                .map(PositionDO::getPointList).collect(Collectors.toList());

        // 历史的坐标
        List<List<Double>> history = Optional.ofNullable(result.getLastPosition())
                .map(positions -> positions.stream().map(VehicleInQueuePositionDTO::getPosition)
                        .collect(Collectors.toList()))
                .map(positions -> positions.stream().map(PositionDO::getPointList).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        // 车道的坐标
        List<List<Double>> lane = Optional.ofNullable(result.getVehicleCurLaneId()).map(hdMapAdapter::getLaneById)
                .map(HdMapElementGeoDO::getCoordinatesList).orElse(Collections.emptyList());

        List<Double> allPreToCurDistance = result.preToCurDistance();
        Double preToCurDistance = result.getVehiclePreToCurDistance();
        ;
        // 下一个车道的坐标
        List<List<Double>> nextLane = Optional.ofNullable(result.getVehicleSuccessorLaneId())
                .map(hdMapAdapter::getLaneById).map(HdMapElementGeoDO::getCoordinatesList)
                .orElse(Collections.emptyList());
        List<String> obsDetail = result.toObstacleDetail();
        System.out.println("本车:" + JacksonUtils.to(curLocation));
        System.out.println("前序车:" + JacksonUtils.to(preLocation));
        System.out.println("前序到本车的距离:" + JacksonUtils.to(preToCurDistance));
        System.out.println("全部前序到本车的距离:" + JacksonUtils.to(allPreToCurDistance));
        System.out.println("障碍物坐标:" + JacksonUtils.to(obsLocation));
        System.out.println("全部障碍物坐标:" + JacksonUtils.to(allObLocationList));
        System.out.println("全部障碍物详细:" + obsDetail);
        System.out.println("历史:" + JacksonUtils.to(history));
        System.out.println("车道:" + JacksonUtils.to(lane));
        System.out.println("next车道:" + JacksonUtils.to(nextLane));

        // }

    }

    @Test
    public void test2() {
        List<TestRunTimeDTO> testRunTimeDTOList = getRunTimeDTOList();
        List<Map<String, Object>> mapList = new ArrayList<>();
        testRunTimeDTOList.forEach(testRunTimeDTO -> {
            try {

                String runtime = testRunTimeDTO.getRuntimeStr();
                String positionHistoryList = testRunTimeDTO.getHistoryStr();
                String area = testRunTimeDTO.getAre();
                Date occurTime = DatetimeUtil.convertDatetimeStr2Date(testRunTimeDTO.getOccurTime());
                VehicleRuntimeInfoContextDO vehicleRuntimeInfoContextDO = JacksonUtils.from(runtime,
                        VehicleRuntimeInfoContextDO.class);
                List<VehicleDataInfoVO> eveThriftResponse = JacksonUtils.from(positionHistoryList,
                        new TypeReference<List<VehicleDataInfoVO>>() {
                        });
                Mockito.doReturn(eveThriftResponse).when(vehicleAdapter)
                        .queryVehicleHistoryDataFromEveReplay(Mockito.any(), Mockito.any(), Mockito.any());
                // "yizhuang_hdmap_v5.38.13.r"
                Mockito.doReturn(area).when(vehicleAdapter).getVehicleHdMapArea(Mockito.any());
                // vehicleRuntimeInfoContextDO.getObstacleContext().setPerceptionObstacle(perceptionObstacleList);
                contextRepository.updateCache(vehicleRuntimeInfoContextDO, new Date().getTime());
                contextRepository.save(vehicleRuntimeInfoContextDO);

                ISCheckActionResult<ISWaitingInQueueResult> checkActionResult = isInQueue
                        .execute(ISCheckActionContext.builder()
                                .item(RiskCheckingQueueItemDO.builder().occurTime(occurTime)
                                        .vin(vehicleRuntimeInfoContextDO.getVin()).build())
                                .vehicleRunTimeContext(vehicleRuntimeInfoContextDO).build());
                ISWaitingInQueueResult result = checkActionResult.getActionResult();
                // 本车的坐标
                List<Double> curLocation = Optional.ofNullable(result.getVehiclePosition())
                        .map(PositionDO::getPointList).orElse(Collections.emptyList());

                // 本车的前序坐标
                List<Double> preLocation = Optional.ofNullable(result.getPreVehiclePosition())
                        .map(VehicleInQueuePositionDTO::getPosition).map(PositionDO::getPointList)
                        .orElse(Collections.emptyList());

                // 障碍物的坐标
                List<Double> obsLocation = Optional.ofNullable(result.getFrontObstacle())
                        .map(VehicleObstacleInfoDTO::getPosition).map(PositionDO::getPointList)
                        .orElse(Collections.emptyList());
                List<List<Double>> allObLocationList = Optional.of(result.getAllObstacleList())
                        .orElse(new ArrayList<>()).stream().map(VehicleObstacleInfoDTO::getPosition)
                        .map(PositionDO::getPointList).collect(Collectors.toList());

                // 历史的坐标
                List<List<Double>> history = Optional.ofNullable(result.getLastPosition())
                        .map(positions -> positions.stream().map(VehicleInQueuePositionDTO::getPosition)
                                .collect(Collectors.toList()))
                        .map(positions -> positions.stream().map(PositionDO::getPointList).collect(Collectors.toList()))
                        .orElse(Collections.emptyList());

                // 车道的坐标
                List<List<Double>> lane = Optional.ofNullable(result.getVehicleCurLaneId())
                        .map(hdMapAdapter::getLaneById).map(HdMapElementGeoDO::getCoordinatesList)
                        .orElse(Collections.emptyList());

                List<Double> allPreToCurDistance = result.preToCurDistance();
                Double preToCurDistance = result.getVehiclePreToCurDistance();
                ;
                // 下一个车道的坐标
                List<List<Double>> nextLane = Optional.ofNullable(result.getVehicleSuccessorLaneId())
                        .map(hdMapAdapter::getLaneById).map(HdMapElementGeoDO::getCoordinatesList)
                        .orElse(Collections.emptyList());
                List<String> obsDetail = result.toObstacleDetail();
                Map<String, Object> map = new HashMap<>();
                map.put("caseId:", testRunTimeDTO.getCaseId());
                map.put("召回类型:", checkActionResult.getCategoryEnum());
                map.put("召回结果:", checkActionResult.getActionResult());
                map.put("本车:", JacksonUtils.to(curLocation));
                map.put("前序车:", JacksonUtils.to(preLocation));
                map.put("前序到本车的距离:", JacksonUtils.to(preToCurDistance));
                map.put("全部前序到本车的距离:", JacksonUtils.to(allPreToCurDistance));
                map.put("障碍物坐标:", JacksonUtils.to(obsLocation));
                map.put("全部障碍物坐标:", JacksonUtils.to(allObLocationList));
                map.put("全部障碍物详细:", obsDetail);
                map.put("历史:", JacksonUtils.to(history));
                map.put("车道:", JacksonUtils.to(lane));
                map.put("next车道:", JacksonUtils.to(nextLane));
                mapList.add(map);
            } catch (Exception e) {
                Map<String, Object> map = new HashMap<>();
                map.put("caseId:", testRunTimeDTO.getCaseId());
                mapList.add(map);
            }

        });
        System.out.println(JacksonUtils.to(mapList));
    }

    @Test
    public void test5() {
        String a = "{\"id\":\"ZBM00220250403092108S05T01\",\"item\":{\"vin\":\"LMTZSV027MC008225\",\"tmpCaseId\":\"ZBM00220250403092108S05T01\",\"eventId\":\"ZBM00220250403092108S05T09\",\"type\":\"VEHICLE_STAND_STILL\",\"source\":\"BEACON_TOWER\",\"recallTime\":\"2025-04-03 09:21:40\",\"occurTime\":\"2025-04-03 09:21:08\",\"confirmedTime\":null,\"closeTime\":null,\"checking\":true,\"checkResult\":null,\"status\":\"VALIDATING\",\"cancelReason\":null,\"confirmReason\":null,\"round\":0,\"maxRound\":6,\"nextRoundTime\":\"2025-04-03 09:22:10\",\"extInfo\":{\"lastCheckResult\":[],\"isNextRoundDynamic\":false},\"createTime\":null,\"updateTime\":null,\"isDeleted\":false},\"lastCheckActionName\":\"ISSwitchPower\",\"currentActionName\":\"ISWaitingInQueueV5\",\"lastCheckResult\":{\"categoryEnum\":\"CANT_FOUND_ANY\",\"needReCheck\":false,\"actionName\":\"ISSwitchPower\",\"actionResult\":null,\"startCheckTime\":\"2025-04-03 09:21:40\",\"duration\":0,\"caseId\":\"ZBM00220250403092108S05T01\"},\"vehicleRunTimeContext\":{\"vin\":\"LMTZSV027MC008225\",\"driveMode\":\"AUTONOMOUS_DRIVING\",\"speed\":0,\"batterySwitching\":false,\"bizStatus\":\"\",\"trafficLightType\":\"RED\",\"distanceToJunction\":0,\"distanceToNextJunction\":-1,\"lng\":116.496035,\"lat\":39.74629,\"positionType\":null,\"oppositeWithRoad\":false,\"drivingOnTrafficLineType\":\"NONE\",\"distanceToFrontConstructionZone\":-1,\"pathOverlapWithConstructionZone\":false,\"waitingGatePole\":false,\"obstacleContext\":{\"perceptionObstacle\":[{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.44721359548826395,\"y\":0.8944271910055706,\"z\":0},\"height\":2.047650933265686,\"id\":337284,\"length\":0.44721359480172396,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":516},\"position\":{\"x\":456820.4799998428,\"y\":4399709.259998486,\"z\":20.794404342686196},\"theta\":1.1071487172120136,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.7155417527537793},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.5507059504081133,\"y\":0.8346993208226345,\"z\":0},\"height\":1.9662798047065735,\"id\":337551,\"length\":0.4634594013914466,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":516},\"position\":{\"x\":456828.8851110744,\"y\":4399717.211368467,\"z\":20.885277037221293},\"theta\":0.9875865713167543,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.45845522941090167},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.451284425169249,\"y\":-0.8923801698809841,\"z\":0},\"height\":1.1341858357191086,\"id\":341430,\"length\":0.19409237802028656,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":456828.1500180131,\"y\":4399709.302753218,\"z\":20.590547451312897},\"theta\":-1.1025921871955326,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.0933560230769217},{\"acceleration\":{\"x\":0,\"y\":0,\"z\":0},\"confidence\":1,\"direction\":{\"x\":0.7048161830537074,\"y\":-0.7093899830879612,\"z\":0},\"height\":1.277603597380221,\"id\":341654,\"length\":0.45295303873717785,\"obstacleType\":{\"coarseType\":\"OTHER\",\"fineType\":\"FINE_OTHER_UNDRIVABLE\"},\"position\":{\"x\":456830.1904622223,\"y\":4399710.598235526,\"z\":20.69793654757957},\"theta\":-0.788632334315592,\"type\":\"UNKNOWN\",\"velocity\":{\"x\":0,\"y\":0,\"z\":0},\"width\":0.16133938170969486}]},\"fenceContext\":{\"fenceAndFieldMetas\":[{\"perceptionId\":-1,\"stopDistanceToEgo\":0.2716199394779415,\"position\":{\"x\":456823.01187315164,\"y\":4399720.066644156},\"constraintSourceType\":\"TRAFFIC_LIGHT\"}]},\"lastUpdateTime\":\"2025-04-03 09:21:40\",\"stagnationCounter\":{\"rule\":\"#context.speed <= 0.0000036\",\"startTime\":\"2025-04-03 09:21:08\",\"endTime\":null,\"duration\":32},\"redLightCounter\":{\"rule\":\"#context.trafficLightType?.code == 1\",\"startTime\":\"2025-04-03 09:21:00\",\"endTime\":null,\"duration\":40},\"curTrafficLight\":{\"color\":\"RED\",\"status\":\"NORMAL\",\"criticalLevel\":\"TrafficLightCriticalLevel_LOW\",\"countdown\":null,\"id\":\"s_9562100b922e\"}},\"checkResultHistory\":[{\"categoryEnum\":\"CANT_FOUND_ANY\",\"needReCheck\":false,\"actionName\":\"ISInMenderParkingArea\",\"actionResult\":null,\"startCheckTime\":\"2025-04-03 09:21:40\",\"duration\":0,\"caseId\":\"ZBM00220250403092108S05T01\"},{\"categoryEnum\":\"CANT_FOUND_ANY\",\"needReCheck\":false,\"actionName\":\"ISInParkingArea\",\"actionResult\":null,\"startCheckTime\":\"2025-04-03 09:21:40\",\"duration\":0,\"caseId\":\"ZBM00220250403092108S05T01\"}],\"continueCheck\":true,\"checkScene\":\"MARK\",\"markVersion\":\"wait_in_queue_fully\",\"actionConfigMap\":{\"ISWaitingInQueueV5\":{\"range\":10,\"pastSecond\":10,\"preMinDistance\":3,\"fineTypeList\":[\"CAR\",\"BUS\",\"VAN\",\"TRUCK\",\"MINI_CAR\",\"CONSTRUCTION_VEHICLE\",\"FORKLIFT_TRUCK\",\"TRAM\",\"SANITATION_VEHICLE\",\"SCHOOL_BUS\",\"FIRE_FIGHTING_TRUCK\",\"POLICE_CAR\",\"AMBULANCE\",\"MEITUAN_MID_CAR_S20\",\"TRICYCLIST\"],\"fineTypeDistanceThreshold\":{\"BUS\":12,\"TRUCK\":12},\"defaultDistanceThreshold\":10,\"angleThresholdByObstacleType\":{\"CAR\":10,\"DEFAULT\":15},\"laneTypeList\":[\"CITY_DRIVING\",\"MIXED\",\"BIKING\"],\"thresholdOpenUsableLaneChecking\":60,\"usableLaneObstacleAngleThreshold\":120,\"usableLaneObstacleDistanceThreshold\":12,\"thresholdOpenBikingLaneChecking\":40,\"sensitiveLaneTypeList\":[\"BIKING\",\"MIXED\"],\"behindObstacleCheckConfig\":{\"enable\":true,\"fineTypeList\":[\"CAR\",\"BUS\",\"VAN\",\"TRUCK\",\"MINI_CAR\",\"CONSTRUCTION_VEHICLE\",\"FORKLIFT_TRUCK\",\"TRAM\",\"SANITATION_VEHICLE\",\"SCHOOL_BUS\",\"FIRE_FIGHTING_TRUCK\",\"POLICE_CAR\",\"AMBULANCE\",\"TRICYCLIST\"],\"angle\":170,\"distance\":10},\"extendObstacleCheckConfig\":{\"enable\":false,\"distance\":10,\"fineType\":[\"MEITUAN_MID_CAR_S20\",\"OTHER_MID_CAR\"],\"searchDistance\":50},\"crossLineWaitCheckConfig\":{\"enable\":false,\"defaultVehicleWidth\":1.2},\"switchObstacleWaitCheckConfig\":{\"enable\":false,\"fineType\":[\"CAR\",\"BUS\",\"VAN\",\"TRUCK\",\"MINI_CAR\",\"CONSTRUCTION_VEHICLE\",\"FORKLIFT_TRUCK\",\"TRAM\",\"SANITATION_VEHICLE\",\"SCHOOL_BUS\",\"FIRE_FIGHTING_TRUCK\",\"POLICE_CAR\",\"AMBULANCE\",\"TRICYCLIST\"],\"frontSwitchMaxTime\":1,\"behindSwitchMaxTime\":0},\"enableUsableLaneFromFc\":false}}}";
        ISCheckActionContext isCheckActionContext = JacksonUtils.from(a, ISCheckActionContext.class);
        String b = "[{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:20:53\",\"longitude\":116.4956446,\"latitude\":39.7463553,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:20:54\",\"longitude\":116.4956447,\"latitude\":39.7463553,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:20:55\",\"longitude\":116.4956447,\"latitude\":39.7463553,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:20:56\",\"longitude\":116.4956472,\"latitude\":39.7463547,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:20:57\",\"longitude\":116.4956646,\"latitude\":39.746351,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:20:58\",\"longitude\":116.4956974,\"latitude\":39.746344,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:20:59\",\"longitude\":116.4957387,\"latitude\":39.7463359,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:21:00\",\"longitude\":116.4957858,\"latitude\":39.7463277,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:21:01\",\"longitude\":116.4958323,\"latitude\":39.7463159,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:21:02\",\"longitude\":116.4958766,\"latitude\":39.7463014,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:21:03\",\"longitude\":116.4959189,\"latitude\":39.7462893,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:21:04\",\"longitude\":116.4959572,\"latitude\":39.7462827,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:21:05\",\"longitude\":116.4959899,\"latitude\":39.746281,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:21:06\",\"longitude\":116.4960144,\"latitude\":39.7462836,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"},{\"vin\":\"LMTZSV027MC008225\",\"time\":\"2025-04-03 09:21:07\",\"longitude\":116.4960295,\"latitude\":39.7462877,\"drive_status_enum\":1,\"drive_status\":\"自动驾驶状态\",\"position_source\":\"autocar_utm\"}]";
        List<VehicleDataInfoVO> list = JacksonUtils.from(b, new TypeReference<List<VehicleDataInfoVO>>() {});
        Mockito.doReturn(list).when(vehicleAdapter).queryVehicleHistoryDataFromEveReplay(Mockito.any(), Mockito.any(),
                Mockito.any());
        Mockito.doReturn("yizhuang").when(vehicleAdapter).getVehicleHdMapArea(Mockito.any());

        isInQueue.execute(isCheckActionContext);
    }

    @SneakyThrows
    private List<TestRunTimeDTO> getRunTimeDTOList() {
        ClassPathResource resource = new ClassPathResource("waitinqueue.txt");
        File file = resource.getFile();
        List<String> list = FileUtils.readLines(file, "UTF-8");
        // 每6行构成TestRunTimeDTO
        List<TestRunTimeDTO> result = new ArrayList<>();
        for (int i = 0; i < list.size(); i += 6) {
            TestRunTimeDTO testRunTimeDTO = new TestRunTimeDTO();
            testRunTimeDTO.setCaseId(list.get(i));
            testRunTimeDTO.setVin(list.get(i + 1));
            testRunTimeDTO.setOccurTime(list.get(i + 2));
            testRunTimeDTO.setRuntimeStr(list.get(i + 3));
            testRunTimeDTO.setHistoryStr(list.get(i + 4));
            testRunTimeDTO.setAre(list.get(i + 5));
            result.add(testRunTimeDTO);
        }
        return result;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class TestRunTimeDTO {

        private String are;
        private String caseId;
        private String vin;
        private String occurTime;
        private String runtimeStr;
        private String historyStr;

    }

    @Test
    public void test3() {
        ISCheckActionContext isCheckActionContext = ISCheckActionContext.builder()
                .item(RiskCheckingQueueItemDO.builder().occurTime(
                        new Date(0L)).build())
                .vehicleRunTimeContext(VehicleRuntimeInfoContextDO.builder().vin("LMTZSV023NC092853").build()).build();
        isSwitchPower.execute(isCheckActionContext);
    }

    @Test
    public void test4() {
        RiskCheckingQueueItemDO riskCheckingQueueItemDO =
                RiskCheckingQueueItemDO.builder().eventId("MT0790220250210095624S05T09").occurTime(new Date()).build();
        VehicleRuntimeInfoContextDO runtimeInfoContextDO = VehicleRuntimeInfoContextDO.builder()
                .lat("22.71318313825776")
                .lng("114.0304980439924")
                .trafficLightType(TrafficLightTypeEnum.RED)
                .distanceToNextJunction(100)
                .vin("LMTZSV023NC092853").build();
        ISCheckActionContext isCheckActionContext = ISCheckActionContext.builder()
                .item(riskCheckingQueueItemDO)
                .vehicleRunTimeContext(runtimeInfoContextDO).build();
        isRandomForestClassify.execute(isCheckActionContext);
    }

    @Test
    public void test6() {
        RiskCheckingQueueItemDO riskCheckingQueueItemDO =
                RiskCheckingQueueItemDO.builder().eventId("MT0790220250210095624S05T09").occurTime(new Date()).build();
        VehicleRuntimeInfoContextDO runtimeInfoContextDO = VehicleRuntimeInfoContextDO.builder()
                .lat("22.71318313825776")
                .lng("114.0304980439924")
                .trafficLightType(TrafficLightTypeEnum.RED)
                .distanceToNextJunction(100)
                .vin("LMTZSV023NC092853").build();
        ISCheckActionContext isCheckActionContext = ISCheckActionContext.builder()
                .item(riskCheckingQueueItemDO)
                .vehicleRunTimeContext(runtimeInfoContextDO).build();
        isRandomForestClassifyV2.execute(isCheckActionContext);
    }




@Test
public void testIsPredecessorOnlyHasThisSuccessorWithPredecessorObj() throws Exception {
    String curLaneId = "lane_1";
    String preLaneId = "lane_pre";
    HdMapElementGeoDO curLane = new HdMapElementGeoDO();
    HdMapElementGeoDO preLane = new HdMapElementGeoDO();
    java.lang.reflect.Field idField = HdMapElementGeoDO.class.getDeclaredField("id");
    idField.setAccessible(true);
    idField.set(curLane, curLaneId);
    idField.set(preLane, preLaneId);

    // 1. 正常情况
    Map<String, Object> curLaneProps = new HashMap<>();
    curLaneProps.put("predecessor", Collections.singletonList(preLaneId));
    curLane.setProperties(curLaneProps);
    Map<String, Object> preLaneProps = new HashMap<>();
    preLaneProps.put("successor", Collections.singletonList(curLaneId));
    preLane.setProperties(preLaneProps);
    Assert.assertTrue(isInQueue.isNotFromMultiTurnForkStrictTest(curLane, preLane));

    // 2. 当前车道无前继
    curLaneProps.put("predecessor", Collections.emptyList());
    curLane.setProperties(curLaneProps);
    Assert.assertFalse(isInQueue.isNotFromMultiTurnForkStrictTest(curLane, preLane));

    // 3. 当前车道有多个前继
    curLaneProps.put("predecessor", Arrays.asList(preLaneId, "other_pre"));
    curLane.setProperties(curLaneProps);
    Assert.assertFalse(isInQueue.isNotFromMultiTurnForkStrictTest(curLane, preLane));

    // 4. 前继车道为null
    curLaneProps.put("predecessor", Collections.singletonList(preLaneId));
    curLane.setProperties(curLaneProps);
    Assert.assertFalse(isInQueue.isNotFromMultiTurnForkStrictTest(curLane, null));

    // 5. 前继车道无后继
    preLaneProps.put("successor", Collections.emptyList());
    preLane.setProperties(preLaneProps);
    Assert.assertFalse(isInQueue.isNotFromMultiTurnForkStrictTest(curLane, preLane));

    // 6. 前继车道有多个后继
    preLaneProps.put("successor", Arrays.asList(curLaneId, "lane_other"));
    preLane.setProperties(preLaneProps);
    Assert.assertFalse(isInQueue.isNotFromMultiTurnForkStrictTest(curLane, preLane));

    // 7. 前继车道唯一后继不是当前车道
    preLaneProps.put("successor", Collections.singletonList("other_lane"));
    preLane.setProperties(preLaneProps);
    Assert.assertFalse(isInQueue.isNotFromMultiTurnForkStrictTest(curLane, preLane));
}
}
