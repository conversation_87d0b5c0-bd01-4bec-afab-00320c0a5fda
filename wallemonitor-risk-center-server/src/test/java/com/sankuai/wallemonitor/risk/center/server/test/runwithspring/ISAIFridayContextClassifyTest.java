package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISAIFridayContextClassify;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.SFTFridayContextClassifyResultVTO;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.SFTFridayContextClassifyResultVTO.SFTFridayContextClassifyResultVTOItem;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class ISAIFridayContextClassifyTest {

    @Resource
    private ISAIFridayContextClassify fridayContextClassify;

    @Test
    public void testExecute() {
        log.info("开始测试 ISAIFridayContextClassify.execute()");

        // 创建测试上下文
        ISCheckActionContext actionContext = createTestContext();

        try {
            // 执行测试
            ISCheckActionResult<SFTFridayContextClassifyResultVTO> result = fridayContextClassify.execute(actionContext);

            // 输出结果
            if (result != null) {
                log.info("测试执行成功！");
                log.info("分类结果: {}", result.getCategoryEnum());
                if (result.getActionResult() != null) {
                    SFTFridayContextClassifyResultVTO actionResult = result.getActionResult();
                    log.info("最终预测: {}", actionResult.getFinalPredict());
                    log.info("所有数据条数: {}", actionResult.getData().size());

                    // 详细输出每个数据项的信息
                    for (int i = 0; i < actionResult.getData().size(); i++) {
                        SFTFridayContextClassifyResultVTOItem item = actionResult.getData().get(i);
                        log.info("数据项[{}] - 检查分类: {}", i, item.getCheckCategory());
                        if (item.getPredictedInfo() != null) {
                            log.info("数据项[{}] - 预测信息: 类型={}, 接管={}",
                                    i,
                                    item.getPredictedInfo().getCategory(),
                                    item.getPredictedInfo().getShouldBeTakeOver());
                        }
                    }
                }
            } else {
                log.error("执行结果为空");
            }

        } catch (Exception e) {
            log.error("测试执行失败", e);
        }

        log.info("测试完成");
    }

    /**
     * 创建测试上下文
     */
    private ISCheckActionContext createTestContext() {
        // 创建风险检查队列项
        RiskCheckingQueueItemDO queueItem = RiskCheckingQueueItemDO.builder()
                .occurTime(new Date())
                .build();

        // 创建车辆运行时信息（模拟真实数据）
        VehicleRuntimeInfoContextDO vehicleContext = createMockVehicleContext();

        // 创建检查动作上下文
        return new ISCheckActionContext() {
            @Override
            public RiskCheckingQueueItemDO getItem() {
                return queueItem;
            }

            @Override
            public VehicleRuntimeInfoContextDO getVehicleRunTimeContext() {
                return vehicleContext;
            }
        };
    }

    /**
     * 创建模拟的车辆上下文数据
     */
    private VehicleRuntimeInfoContextDO createMockVehicleContext() {
        return VehicleRuntimeInfoContextDO.builder()
                .vin("LMTZSV025MC027307")
                .speed(0.0)
                .build();
    }
}
